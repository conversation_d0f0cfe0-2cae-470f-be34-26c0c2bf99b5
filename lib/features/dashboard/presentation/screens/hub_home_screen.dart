import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// New hub-style home screen with feature cards
class HubHomeScreen extends ConsumerWidget {
  const HubHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context, theme),

            // Feature cards grid
            Expanded(
              child: _buildFeatureGrid(context, theme),
            ),

            // Pro tip section
            _buildProTip(context, theme),

            // Bottom padding for FAB
            const SizedBox(height: 80),
          ],
        ),
      ),
    ).withGlobalFabs(
      showBackFab: false,
      showHomeFab: false,
      showAddTransactionFab: true,
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          Text(
            'BudApp',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: AppTypography.fontWeightBold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Your Budget Assistant 🤖',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureGrid(BuildContext context, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      child: GridView.count(
        crossAxisCount: 2,
        crossAxisSpacing: AppSpacing.md,
        mainAxisSpacing: AppSpacing.md,
        childAspectRatio: 1.1,
        children: [
          _buildFeatureCard(
            context: context,
            theme: theme,
            icon: Icons.account_balance_wallet,
            iconColor: theme.colorScheme.primary,
            title: 'Accounts',
            subtitle: 'Manage your financial accounts',
            actionText: 'Track balances',
            onTap: () => context.push('/accounts'),
          ),
          _buildFeatureCard(
            context: context,
            theme: theme,
            icon: Icons.receipt_long,
            iconColor: Colors.blue,
            title: 'Transactions',
            subtitle: 'View your transaction history',
            actionText: 'Recent activity',
            onTap: () => context.push('/transactions'),
          ),
          _buildFeatureCard(
            context: context,
            theme: theme,
            icon: Icons.trending_up,
            iconColor: Colors.red,
            title: 'Budgets',
            subtitle: 'Track your spending goals',
            actionText: 'Monthly budgets',
            onTap: () => context.push('/budgets'),
          ),
          _buildFeatureCard(
            context: context,
            theme: theme,
            icon: Icons.track_changes,
            iconColor: Colors.purple,
            title: 'Goals',
            subtitle: 'Track your savings goals',
            actionText: 'Progress tracking',
            onTap: () => context.push('/goals'),
          ),
          _buildFeatureCard(
            context: context,
            theme: theme,
            icon: Icons.bar_chart,
            iconColor: Colors.orange,
            title: 'Statistics',
            subtitle: 'Analyze spending patterns',
            actionText: 'Insights & charts',
            onTap: () => _showComingSoon(context),
          ),
          _buildFeatureCard(
            context: context,
            theme: theme,
            icon: Icons.settings,
            iconColor: Colors.grey,
            title: 'Settings',
            subtitle: 'Profile and app settings',
            actionText: 'Preferences',
            onTap: () => context.push('/profile'),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard({
    required BuildContext context,
    required ThemeData theme,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String actionText,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 28,
                ),
              ),
              const SizedBox(height: AppSpacing.md),

              // Title
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTypography.fontWeightSemiBold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSpacing.xs),

              // Subtitle
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSpacing.sm),

              // Action text
              Text(
                actionText,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: iconColor,
                  fontWeight: AppTypography.fontWeightMedium,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProTip(BuildContext context, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.lg),
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Pro Tip',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  'Start by setting up your accounts, then create budgets to track your spending goals!',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Statistics feature coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
